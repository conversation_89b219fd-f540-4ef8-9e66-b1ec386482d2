import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  Card,
  CardContent,
  IconButton,
} from '@mui/material';
import {
  Close as CloseIcon,
  Inventory as InventoryIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';

interface StockDetailProps {
  open: boolean;
  onClose: () => void;
  itemId: string;
}

interface StockTransaction {
  id: string;
  date: string;
  type: 'opening' | 'purchase' | 'sale' | 'purchase_return' | 'sales_return';
  description: string;
  quantity: number;
  rate: number;
  total: number;
  voucherId?: string;
}

interface StockDetailData {
  id: string;
  category: string;
  currentStock: number;
  totalValue: number;
  averageRate: number;
  transactions: StockTransaction[];
  summary: {
    totalOpening: number;
    totalPurchases: number;
    totalSales: number;
    totalPurchaseReturns: number;
    totalSalesReturns: number;
  };
}

const StockDetail: React.FC<StockDetailProps> = ({ open, onClose, itemId }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [stockData, setStockData] = useState<StockDetailData | null>(null);

  // Fetch stock details
  const fetchStockDetails = async () => {
    if (!itemId) return;

    try {
      setLoading(true);
      setError('');

      const token = localStorage.getItem('token');
      if (!token) {
        setError('No authentication token found');
        return;
      }

      const response = await fetch(`http://localhost:5000/api/inventory/${itemId}/details`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'dbprefix': 'default'
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setStockData(result.data);
      } else {
        // If API doesn't exist yet, create mock data
        setStockData(createMockStockData(itemId));
      }

    } catch (err: any) {
      console.error('Error fetching stock details:', err);
      // Create mock data for demonstration
      setStockData(createMockStockData(itemId));
    } finally {
      setLoading(false);
    }
  };

  // Create mock data for demonstration
  const createMockStockData = (id: string): StockDetailData => {
    return {
      id,
      category: `Category ${id}`,
      currentStock: Math.floor(Math.random() * 1000) + 100,
      totalValue: Math.floor(Math.random() * 50000) + 10000,
      averageRate: Math.floor(Math.random() * 100) + 50,
      transactions: [
        {
          id: '1',
          date: '2024-01-15',
          type: 'opening',
          description: 'Opening Stock',
          quantity: 500,
          rate: 75,
          total: 37500,
        },
        {
          id: '2',
          date: '2024-01-20',
          type: 'purchase',
          description: 'Purchase from Supplier A',
          quantity: 200,
          rate: 80,
          total: 16000,
          voucherId: 'PV-001',
        },
        {
          id: '3',
          date: '2024-01-25',
          type: 'sale',
          description: 'Sale to Customer B',
          quantity: -150,
          rate: 95,
          total: -14250,
          voucherId: 'SV-001',
        },
        {
          id: '4',
          date: '2024-01-30',
          type: 'purchase_return',
          description: 'Return to Supplier A',
          quantity: -25,
          rate: 80,
          total: -2000,
          voucherId: 'PRV-001',
        },
      ],
      summary: {
        totalOpening: 500,
        totalPurchases: 200,
        totalSales: 150,
        totalPurchaseReturns: 25,
        totalSalesReturns: 0,
      },
    };
  };

  useEffect(() => {
    if (open && itemId) {
      fetchStockDetails();
    }
  }, [open, itemId]);

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'opening': return '#2196F3';
      case 'purchase': return '#4CAF50';
      case 'sale': return '#FF5722';
      case 'purchase_return': return '#FF9800';
      case 'sales_return': return '#9C27B0';
      default: return '#666';
    }
  };

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'opening': return 'Opening';
      case 'purchase': return 'Purchase';
      case 'sale': return 'Sale';
      case 'purchase_return': return 'P Return';
      case 'sales_return': return 'S Return';
      default: return type;
    }
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const formatCurrency = (num: number) => {
    return num.toFixed(2);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        backgroundColor: '#D9E1FA',
        color: '#333',
        fontWeight: 'bold'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <InventoryIcon />
          <Typography variant="h6" component="span">
            Stock Details - Item {itemId}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
            <CircularProgress />
            <Typography sx={{ ml: 2 }}>Loading stock details...</Typography>
          </Box>
        ) : error ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography color="error">{error}</Typography>
            <Button onClick={fetchStockDetails} sx={{ mt: 2 }}>
              Retry
            </Button>
          </Box>
        ) : stockData ? (
          <Box>
            {/* Summary Cards */}
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ backgroundColor: '#E3F2FD' }}>
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <InventoryIcon sx={{ fontSize: 40, color: '#1976D2', mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      {formatNumber(stockData.currentStock)}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Current Stock
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ backgroundColor: '#E8F5E8' }}>
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <TrendingUpIcon sx={{ fontSize: 40, color: '#388E3C', mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      {formatCurrency(stockData.totalValue)}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Total Value
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ backgroundColor: '#FFF3E0' }}>
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <AssessmentIcon sx={{ fontSize: 40, color: '#F57C00', mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      {formatCurrency(stockData.averageRate)}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Average Rate
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ backgroundColor: '#FCE4EC' }}>
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <TrendingDownIcon sx={{ fontSize: 40, color: '#C2185B', mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      {stockData.transactions.length}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Transactions
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Divider sx={{ my: 2 }} />

            {/* Transaction History */}
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Transaction History
            </Typography>
            
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                    <TableCell sx={{ fontWeight: 'bold' }}>Date</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Type</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>Quantity</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>Rate</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>Total</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Voucher</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {stockData.transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        {new Date(transaction.date).toLocaleDateString('en-GB')}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getTransactionTypeLabel(transaction.type)}
                          size="small"
                          sx={{
                            backgroundColor: getTransactionTypeColor(transaction.type),
                            color: 'white',
                            fontWeight: 'bold',
                          }}
                        />
                      </TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell sx={{ textAlign: 'right' }}>
                        {formatNumber(Math.abs(transaction.quantity))}
                      </TableCell>
                      <TableCell sx={{ textAlign: 'right' }}>
                        {formatCurrency(transaction.rate)}
                      </TableCell>
                      <TableCell sx={{ textAlign: 'right' }}>
                        {formatCurrency(Math.abs(transaction.total))}
                      </TableCell>
                      <TableCell>
                        {transaction.voucherId || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        ) : (
          <Typography>No data available</Typography>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} variant="contained" color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default StockDetail;
