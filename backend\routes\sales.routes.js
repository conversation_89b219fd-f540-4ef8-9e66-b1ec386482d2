const express = require("express");
const router = express.Router();
const salesController = require("../controllers/salesController");
const verifyToken = require("../middleware/auth");

router.post("/", verifyToken, salesController.addSalesVoucher);
router.get("/", verifyToken, salesController.getAllSales);
router.get("/:id", verifyToken, salesController.getSalesById);
router.put("/:id", verifyToken, salesController.updateSalesVoucher);
router.delete("/:id", verifyToken, salesController.deleteSalesVoucher);

module.exports = router;
