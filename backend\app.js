// app.js
const express = require("express");
const mongoose = require("mongoose");
const dotenv = require("dotenv");
const cors = require("cors");

// Load env variables
dotenv.config();

// App init
const app = express();
app.use(cors());
app.use(express.json());

// Routes
const voucherRoutes = require("./routes/voucher.routes");
app.use("/api/vouchers", voucherRoutes);

// Default route
app.get("/", (req, res) => {
  res.send("Inventory + Accounting API is running!");
});

module.exports = app;
