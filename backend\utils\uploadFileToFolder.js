const path = require("path");
const fs = require("fs");

const uploadFileToFolder = (file, folderPath, fileName) => {
  return new Promise((resolve, reject) => {
    try {
      // Ensure the folder exists
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
      }

      const filePath = path.join(folderPath, fileName);
      fs.writeFile(filePath, file.buffer, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(filePath); // Return saved file path
        }
      });
    } catch (err) {
      reject(err);
    }
  });
};

module.exports = uploadFileToFolder;
