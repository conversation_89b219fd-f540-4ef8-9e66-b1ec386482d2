const mongoose = require("mongoose");

const accountSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },              // e.g., "Cash", "Inventory"
    type: { type: String, required: true },              // e.g., "Asset", "Liability"
    openingBalance: { type: Number, default: 0 },
    balanceType: { type: String, enum: ["debit", "credit"], default: "debit" },
    description: { type: String },
    isActive: { type: Boolean, default: true },
    branch: { type: String },                            // Optional for multi-branch setups
    metadata: { type: mongoose.Schema.Types.Mixed, default: {} },
    addedBy: { type: String, default: "admin" },
    updatedBy: { type: String, default: "admin" },
  },
  {
    timestamps: true,
  }
);

module.exports = { AccountSchema: accountSchema };
