const getDbConnection = require("../utils/dbConnector");
const { voucherSchema } = require("../models/voucher.model");
const { entriesSchema } = require("../models/entries.model");
const { inventorySchema } = require("../models/inventory.model");
const { AccountSchema } = require("../models/account.model");
const getModel = require("../utils/getModel");

exports.addPurchase = async (req, res) => {
  try {
    const dbprefix = req.headers.dbprefix;
    const connection = await getDbConnection(dbprefix);

    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Inventory = getModel(connection, "Inventory", inventorySchema);

    const { date, supplierAccount, items, metadata } = req.body;
    const created_by = req.user?.id;

    // Create voucher
    const voucher = new Voucher({
      voucher_id: undefined, // Will be set below
      date,
      type: "purchase",
      created_by,
      updated_by: created_by,
      accounts: [supplierAccount, ...items.map(i => i.account)].filter(Boolean),
      is_void: false,
      is_posted: true,
      metadata: metadata || {},
      file: null,
    });

    // Generate voucher_id (auto-increment)
    let nextVoucherNumber = 1;
    const latest = await Voucher.find({ type: "purchase" }).sort({ created_at: -1 }).limit(1);
    if (latest.length) {
      const last = parseInt(latest[0].voucher_id?.split("-")[1]);
      if (!isNaN(last)) nextVoucherNumber = last + 1;
    }
    voucher.voucher_id = `PV-${nextVoucherNumber}`;

    const savedVoucher = await voucher.save();

    // Prepare entries: Debit Inventory, Credit Supplier
    const entryDocs = [];
    let totalAmount = 0;
    for (const item of items) {
      // Debit Inventory
      entryDocs.push({
        accountId: item.account, // Inventory account
        entries: [{
          voucherId: savedVoucher._id,
          type: "purchase",
          description: item.description || "",
          debit: item.amount,
          credit: 0,
          isVoid: false,
          isPosted: true,
          file: "",
          date: date,
          metadata: item.metadata || {},
        }]
      });
      // Update stock
      await Inventory.findByIdAndUpdate(item.itemId, { $inc: { quantity: item.quantity } });
      totalAmount += item.amount;
    }
    // Credit Supplier
    entryDocs.push({
      accountId: supplierAccount,
      entries: [{
        voucherId: savedVoucher._id,
        type: "purchase",
        description: "Purchase from supplier",
        debit: 0,
        credit: totalAmount,
        isVoid: false,
        isPosted: true,
        file: "",
        date: date,
        metadata: {},
      }]
    });

    await Entries.insertMany(entryDocs);

    res.status(201).json({
      message: "Purchase voucher created",
      voucher: savedVoucher,
      entries: entryDocs,
    });
  } catch (error) {
    res.status(500).json({ message: "Error adding purchase voucher", error: error.message });
  }
};

exports.getAllPurchases = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);

    const { page = 1, limit = 25 } = req.query;
    const filter = { type: "purchase" };

    const totalCount = await Voucher.countDocuments(filter);
    const purchases = await Voucher.find(filter)
      .sort({ created_at: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit))
      .lean();

    res.json({
      items: purchases,
      totalCount,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(totalCount / limit),
    });
  } catch (error) {
    res.status(500).json({ message: "Error fetching purchases", error: error.message });
  }
};

exports.getPurchaseById = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);

    const voucher = await Voucher.findOne({ _id: req.params.id, type: "purchase" });
    if (!voucher) return res.status(404).json({ message: "Purchase voucher not found" });

    const entries = await Entries.find({ "entries.voucherId": voucher._id });

    res.json({ voucher, entries });
  } catch (error) {
    res.status(500).json({ message: "Error fetching purchase voucher", error: error.message });
  }
};

exports.updatePurchase = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Inventory = getModel(connection, "Inventory", inventorySchema);

    const { id } = req.params;
    const { date, supplierAccount, items, metadata } = req.body;
    const updated_by = req.user?.id;

    const voucher = await Voucher.findOne({ _id: id, type: "purchase" });
    if (!voucher) return res.status(404).json({ message: "Purchase voucher not found" });

    // Reverse stock for old items
    const oldEntries = await Entries.find({ "entries.voucherId": voucher._id });
    for (const doc of oldEntries) {
      for (const entry of doc.entries) {
        if (entry.type === "purchase" && entry.debit && entry.metadata?.itemId) {
          await Inventory.findByIdAndUpdate(entry.metadata.itemId, { $inc: { quantity: -entry.metadata.quantity } });
        }
      }
    }
    // Remove old entries
    await Entries.deleteMany({ "entries.voucherId": voucher._id });

    // Update voucher fields
    voucher.date = date || voucher.date;
    voucher.accounts = [supplierAccount, ...items.map(i => i.account)].filter(Boolean);
    voucher.updated_by = updated_by;
    voucher.metadata = metadata || voucher.metadata;
    await voucher.save();

    // Add new entries and update stock
    const entryDocs = [];
    let totalAmount = 0;
    for (const item of items) {
      entryDocs.push({
        accountId: item.account,
        entries: [{
          voucherId: voucher._id,
          type: "purchase",
          description: item.description || "",
          debit: item.amount,
          credit: 0,
          isVoid: false,
          isPosted: true,
          file: "",
          date: date,
          metadata: { ...item.metadata, itemId: item.itemId, quantity: item.quantity },
        }]
      });
      await Inventory.findByIdAndUpdate(item.itemId, { $inc: { quantity: item.quantity } });
      totalAmount += item.amount;
    }
    entryDocs.push({
      accountId: supplierAccount,
      entries: [{
        voucherId: voucher._id,
        type: "purchase",
        description: "Purchase from supplier",
        debit: 0,
        credit: totalAmount,
        isVoid: false,
        isPosted: true,
        file: "",
        date: date,
        metadata: {},
      }]
    });

    await Entries.insertMany(entryDocs);

    res.json({ message: "Purchase voucher updated", voucher, entries: entryDocs });
  } catch (error) {
    res.status(500).json({ message: "Error updating purchase voucher", error: error.message });
  }
};

exports.deletePurchase = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Inventory = getModel(connection, "Inventory", inventorySchema);

    const { id } = req.params;
    const voucher = await Voucher.findOne({ _id: id, type: "purchase" });
    if (!voucher) return res.status(404).json({ message: "Purchase voucher not found" });

    // Reverse stock for items
    const entries = await Entries.find({ "entries.voucherId": voucher._id });
    for (const doc of entries) {
      for (const entry of doc.entries) {
        if (entry.type === "purchase" && entry.debit && entry.metadata?.itemId) {
          await Inventory.findByIdAndUpdate(entry.metadata.itemId, { $inc: { quantity: -entry.metadata.quantity } });
        }
      }
    }

    await Voucher.deleteOne({ _id: id });
    await Entries.deleteMany({ "entries.voucherId": voucher._id });

    res.json({ message: "Purchase voucher deleted" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting purchase voucher", error: error.message });
  }
};
