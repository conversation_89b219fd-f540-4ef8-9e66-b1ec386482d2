const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");

const userSchema = new mongoose.Schema(
  {
    username: { type: String, required: true, unique: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true }, // Store hashed password
    role: { type: String, default: "admin" },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt
  }
);

module.exports = { userSchema };
userSchema.statics.seedAdmin = async function () {
  const User = this;
  const exists = await User.findOne({ username: "admin" });
  if (!exists) {
    const hash = await bcrypt.hash("admin@123", 10);
    await User.create({
      username: "admin",
      email: "<EMAIL>",
      password: hash,
      role: "admin",
      isActive: true,
    });
    // Optionally log: console.log("Default admin user created.");
  }
};

module.exports = { userSchema };
