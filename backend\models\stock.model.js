const mongoose = require("mongoose");

const stockSchema = new mongoose.Schema({
  name: { type: String, required: true },                // Item name
  sku: { type: String, unique: true, required: true },   // Stock Keeping Unit / code
  description: { type: String },
  quantity: { type: Number, default: 0 },                // Quantity on hand
  unit: { type: String },                                // e.g., "pcs", "kg"
  purchaseRate: { type: Number, default: 0 },            // Last purchase rate
  saleRate: { type: Number, default: 0 },                // Last sale rate
  category: { type: String },                            // Optional: category/group
  supplier: { type: String },                            // Optional: supplier info
  batchNumber: { type: String },                         // Optional: batch/lot number
  metadata: { type: mongoose.Schema.Types.Mixed, default: {} }, // Any extra info
  isActive: { type: Boolean, default: true },
  isDeleted: { type: Boolean, default: false },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
}, { timestamps: true });

module.exports = { stockSchema };
