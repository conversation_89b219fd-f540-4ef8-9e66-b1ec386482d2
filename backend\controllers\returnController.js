const getDbConnection = require("../utils/dbConnector");
const { voucherSchema } = require("../models/voucher.model");
const { entriesSchema } = require("../models/entries.model");
const { inventorySchema } = require("../models/inventory.model");
const getModel = require("../utils/getModel");

exports.addReturnVoucher = async (req, res) => {
  try {
    const dbprefix = req.headers.dbprefix;
    const connection = await getDbConnection(dbprefix);

    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Inventory = getModel(connection, "Inventory", inventorySchema);

    const { date, returnType, account, items, metadata } = req.body; // returnType: "purchase-return" or "sales-return"
    const created_by = req.user?.id;

    // Create voucher
    let voucherCode = returnType === "purchase-return" ? "PRV" : "SRV";
    let nextVoucherNumber = 1;
    const latest = await Voucher.find({ type: returnType }).sort({ created_at: -1 }).limit(1);
    if (latest.length) {
      const last = parseInt(latest[0].voucher_id?.split("-")[1]);
      if (!isNaN(last)) nextVoucherNumber = last + 1;
    }
    const voucher_id = `${voucherCode}-${nextVoucherNumber}`;

    const voucher = new Voucher({
      voucher_id,
      date,
      type: returnType,
      created_by,
      updated_by: created_by,
      accounts: [account, ...items.map(i => i.account)].filter(Boolean),
      is_void: false,
      is_posted: true,
      metadata: metadata || {},
      file: null,
    });

    const savedVoucher = await voucher.save();

    // Prepare entries and update stock
    const entryDocs = [];
    let totalAmount = 0;
    for (const item of items) {
      // For purchase return: Credit Inventory, Debit Supplier
      // For sales return: Debit Inventory, Credit Customer
      if (returnType === "purchase-return") {
        // Credit Inventory
        entryDocs.push({
          accountId: item.account,
          entries: [{
            voucherId: savedVoucher._id,
            type: returnType,
            description: item.description || "",
            debit: 0,
            credit: item.amount,
            isVoid: false,
            isPosted: true,
            file: "",
            date: date,
            metadata: { ...item.metadata, itemId: item.itemId, quantity: item.quantity },
          }]
        });
        // Update stock (increase)
        await Inventory.findByIdAndUpdate(item.itemId, { $inc: { quantity: item.quantity } });
        totalAmount += item.amount;
      } else if (returnType === "sales-return") {
        // Debit Inventory
        entryDocs.push({
          accountId: item.account,
          entries: [{
            voucherId: savedVoucher._id,
            type: returnType,
            description: item.description || "",
            debit: item.amount,
            credit: 0,
            isVoid: false,
            isPosted: true,
            file: "",
            date: date,
            metadata: { ...item.metadata, itemId: item.itemId, quantity: item.quantity },
          }]
        });
        // Update stock (reduce)
        await Inventory.findByIdAndUpdate(item.itemId, { $inc: { quantity: -item.quantity } });
        totalAmount += item.amount;
      }
    }
    // Counter entry for account (supplier/customer)
    if (returnType === "purchase-return") {
      // Debit Supplier
      entryDocs.push({
        accountId: account,
        entries: [{
          voucherId: savedVoucher._id,
          type: returnType,
          description: "Purchase Return",
          debit: totalAmount,
          credit: 0,
          isVoid: false,
          isPosted: true,
          file: "",
          date: date,
          metadata: {},
        }]
      });
    } else if (returnType === "sales-return") {
      // Credit Customer
      entryDocs.push({
        accountId: account,
        entries: [{
          voucherId: savedVoucher._id,
          type: returnType,
          description: "Sales Return",
          debit: 0,
          credit: totalAmount,
          isVoid: false,
          isPosted: true,
          file: "",
          date: date,
          metadata: {},
        }]
      });
    }

    await Entries.insertMany(entryDocs);

    res.status(201).json({
      message: "Return voucher created",
      voucher: savedVoucher,
      entries: entryDocs,
    });
  } catch (error) {
    res.status(500).json({ message: "Error adding return voucher", error: error.message });
  }
};

exports.getAllReturns = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);

    const { page = 1, limit = 25, returnType } = req.query;
    const filter = { type: { $in: ["purchase-return", "sales-return"] } };
    if (returnType) filter.type = returnType;

    const totalCount = await Voucher.countDocuments(filter);
    const returns = await Voucher.find(filter)
      .sort({ created_at: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit))
      .lean();

    res.json({
      items: returns,
      totalCount,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(totalCount / limit),
    });
  } catch (error) {
    res.status(500).json({ message: "Error fetching return vouchers", error: error.message });
  }
};

exports.getReturnById = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);

    const voucher = await Voucher.findOne({ _id: req.params.id, type: { $in: ["purchase-return", "sales-return"] } });
    if (!voucher) return res.status(404).json({ message: "Return voucher not found" });

    const entries = await Entries.find({ "entries.voucherId": voucher._id });

    res.json({ voucher, entries });
  } catch (error) {
    res.status(500).json({ message: "Error fetching return voucher", error: error.message });
  }
};

exports.updateReturn = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Inventory = getModel(connection, "Inventory", inventorySchema);

    const { id } = req.params;
    const { date, returnType, account, items, metadata } = req.body;
    const updated_by = req.user?.id;

    const voucher = await Voucher.findOne({ _id: id, type: { $in: ["purchase-return", "sales-return"] } });
    if (!voucher) return res.status(404).json({ message: "Return voucher not found" });

    // Reverse stock for old items
    const oldEntries = await Entries.find({ "entries.voucherId": voucher._id });
    for (const doc of oldEntries) {
      for (const entry of doc.entries) {
        if (entry.metadata?.itemId && entry.metadata?.quantity) {
          if (voucher.type === "purchase-return") {
            await Inventory.findByIdAndUpdate(entry.metadata.itemId, { $inc: { quantity: -entry.metadata.quantity } });
          } else if (voucher.type === "sales-return") {
            await Inventory.findByIdAndUpdate(entry.metadata.itemId, { $inc: { quantity: entry.metadata.quantity } });
          }
        }
      }
    }
    // Remove old entries
    await Entries.deleteMany({ "entries.voucherId": voucher._id });

    // Update voucher fields
    voucher.date = date || voucher.date;
    voucher.accounts = [account, ...items.map(i => i.account)].filter(Boolean);
    voucher.updated_by = updated_by;
    voucher.metadata = metadata || voucher.metadata;
    await voucher.save();

    // Add new entries and update stock
    const entryDocs = [];
    let totalAmount = 0;
    for (const item of items) {
      if (returnType === "purchase-return") {
        entryDocs.push({
          accountId: item.account,
          entries: [{
            voucherId: voucher._id,
            type: returnType,
            description: item.description || "",
            debit: 0,
            credit: item.amount,
            isVoid: false,
            isPosted: true,
            file: "",
            date: date,
            metadata: { ...item.metadata, itemId: item.itemId, quantity: item.quantity },
          }]
        });
        await Inventory.findByIdAndUpdate(item.itemId, { $inc: { quantity: item.quantity } });
        totalAmount += item.amount;
      } else if (returnType === "sales-return") {
        entryDocs.push({
          accountId: item.account,
          entries: [{
            voucherId: voucher._id,
            type: returnType,
            description: item.description || "",
            debit: item.amount,
            credit: 0,
            isVoid: false,
            isPosted: true,
            file: "",
            date: date,
            metadata: { ...item.metadata, itemId: item.itemId, quantity: item.quantity },
          }]
        });
        await Inventory.findByIdAndUpdate(item.itemId, { $inc: { quantity: -item.quantity } });
        totalAmount += item.amount;
      }
    }
    if (returnType === "purchase-return") {
      entryDocs.push({
        accountId: account,
        entries: [{
          voucherId: voucher._id,
          type: returnType,
          description: "Purchase Return",
          debit: totalAmount,
          credit: 0,
          isVoid: false,
          isPosted: true,
          file: "",
          date: date,
          metadata: {},
        }]
      });
    } else if (returnType === "sales-return") {
      entryDocs.push({
        accountId: account,
        entries: [{
          voucherId: voucher._id,
          type: returnType,
          description: "Sales Return",
          debit: 0,
          credit: totalAmount,
          isVoid: false,
          isPosted: true,
          file: "",
          date: date,
          metadata: {},
        }]
      });
    }

    await Entries.insertMany(entryDocs);

    res.json({ message: "Return voucher updated", voucher, entries: entryDocs });
  } catch (error) {
    res.status(500).json({ message: "Error updating return voucher", error: error.message });
  }
};

exports.deleteReturn = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Inventory = getModel(connection, "Inventory", inventorySchema);

    const { id } = req.params;
    const voucher = await Voucher.findOne({ _id: id, type: { $in: ["purchase-return", "sales-return"] } });
    if (!voucher) return res.status(404).json({ message: "Return voucher not found" });

    // Reverse stock for items
    const entries = await Entries.find({ "entries.voucherId": voucher._id });
    for (const doc of entries) {
      for (const entry of doc.entries) {
        if (entry.metadata?.itemId && entry.metadata?.quantity) {
          if (voucher.type === "purchase-return") {
            await Inventory.findByIdAndUpdate(entry.metadata.itemId, { $inc: { quantity: -entry.metadata.quantity } });
          } else if (voucher.type === "sales-return") {
            await Inventory.findByIdAndUpdate(entry.metadata.itemId, { $inc: { quantity: entry.metadata.quantity } });
          }
        }
      }
    }

    await Voucher.deleteOne({ _id: id });
    await Entries.deleteMany({ "entries.voucherId": voucher._id });

    res.json({ message: "Return voucher deleted" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting return voucher", error: error.message });
  }
};
