const express = require("express");
const router = express.Router();
const inventoryController = require("../controllers/inventoryController");
const verifyToken = require("../middleware/auth");

router.post("/", verifyToken, inventoryController.createItem);
router.get("/", verifyToken, inventoryController.getItems);
router.get("/:id", verifyToken, inventoryController.getItemById);
router.put("/:id", verifyToken, inventoryController.updateItem);
router.delete("/:id", verifyToken, inventoryController.deleteItem);

module.exports = router;
