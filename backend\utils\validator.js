function isValidObjectId(id) {
  return typeof id === "string" && /^[a-fA-F0-9]{24}$/.test(id);
}

function isNonEmptyString(value) {
  return typeof value === "string" && value.trim().length > 0;
}

function isValidDate(date) {
  const d = (date instanceof Date) ? date : new Date(date);
  return d instanceof Date && !isNaN(d.getTime());
}

function isPositiveNumber(value) {
  return typeof value === "number" && value > 0;
}

module.exports = {
  isValidObjectId,
  isNonEmptyString,
  isValidDate,
  isPositiveNumber,
};
