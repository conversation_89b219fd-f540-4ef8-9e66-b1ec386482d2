const mongoose = require("mongoose");

// Simple Category schema (define inline for controller use)
const categorySchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true, trim: true }
}, { timestamps: true });

function getCategoryModel(connection) {
  return connection.model("Category", categorySchema);
}

const getDbConnection = require("../utils/dbConnector");

exports.createCategory = async (req, res) => {
  try {
    const { name } = req.body;
    if (!name || typeof name !== "string" || !name.trim()) {
      return res.status(400).json({ success: false, message: "Category name is required." });
    }

    const connection = await getDbConnection(req.headers.dbprefix);
    const Category = getCategoryModel(connection);

    const existing = await Category.findOne({ name: name.trim() });
    if (existing) {
      return res.status(409).json({ success: false, message: "Category already exists." });
    }

    const category = new Category({ name: name.trim() });
    await category.save();

    res.status(201).json({ success: true, message: "Category created successfully.", category });
  } catch (error) {
    res.status(500).json({ success: false, message: "Error creating category.", error: error.message });
  }
};

exports.getCategories = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Category = getCategoryModel(connection);

    const categories = await Category.find().sort({ name: 1 });
    res.json({ success: true, categories });
  } catch (error) {
    res.status(500).json({ success: false, message: "Error fetching categories.", error: error.message });
  }
};

exports.deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ success: false, message: "Invalid category ID." });
    }

    const connection = await getDbConnection(req.headers.dbprefix);
    const Category = getCategoryModel(connection);

    const deleted = await Category.findByIdAndDelete(id);
    if (!deleted) {
      return res.status(404).json({ success: false, message: "Category not found." });
    }

    res.json({ success: true, message: "Category deleted successfully." });
  } catch (error) {
    res.status(500).json({ success: false, message: "Error deleting category.", error: error.message });
  }
};
