const getDbConnection = require("../utils/dbConnector");
const { voucherSchema } = require("../models/voucher.model");
const { entriesSchema } = require("../models/entries.model");
const { stockSchema } = require("../models/stock.model");
const getModel = require("../utils/getModel");

exports.addSalesVoucher = async (req, res) => {
  try {
    const dbprefix = req.headers.dbprefix;
    const connection = await getDbConnection(dbprefix);

    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Stock = getModel(connection, "Stock", stockSchema);

    const { date, customerAccount, items, metadata } = req.body;
    const created_by = req.user?.id;

    // Generate voucher_id (auto-increment)
    let nextVoucherNumber = 1;
    const latest = await Voucher.find({ type: "sales" }).sort({ created_at: -1 }).limit(1);
    if (latest.length) {
      const last = parseInt(latest[0].voucher_id?.split("-")[1]);
      if (!isNaN(last)) nextVoucherNumber = last + 1;
    }
    const voucher_id = `SV-${nextVoucherNumber}`;

    // Create voucher
    const voucher = new Voucher({
      voucher_id,
      date,
      type: "sales",
      created_by,
      updated_by: created_by,
      accounts: [customerAccount, ...items.map(i => i.account)].filter(Boolean),
      is_void: false,
      is_posted: true,
      metadata: metadata || {},
      file: null,
    });

    const savedVoucher = await voucher.save();

    // Prepare entries: Debit Customer, Credit Sales, and update stock
    const entryDocs = [];
    let totalAmount = 0;
    for (const item of items) {
      // Credit Sales
      entryDocs.push({
        accountId: item.account, // Sales account
        entries: [{
          voucherId: savedVoucher._id,
          type: "sales",
          description: item.description || "",
          debit: 0,
          credit: item.amount,
          isVoid: false,
          isPosted: true,
          file: "",
          date: date,
          metadata: { ...item.metadata, itemId: item.itemId, quantity: item.quantity },
        }]
      });
      // Deduct stock
      await Stock.findByIdAndUpdate(item.itemId, { $inc: { quantity: -item.quantity } });
      totalAmount += item.amount;
    }
    // Debit Customer
    entryDocs.push({
      accountId: customerAccount,
      entries: [{
        voucherId: savedVoucher._id,
        type: "sales",
        description: "Sales to customer",
        debit: totalAmount,
        credit: 0,
        isVoid: false,
        isPosted: true,
        file: "",
        date: date,
        metadata: {},
      }]
    });

    await Entries.insertMany(entryDocs);

    res.status(201).json({
      message: "Sales voucher created",
      voucher: savedVoucher,
      entries: entryDocs,
    });
  } catch (error) {
    res.status(500).json({ message: "Error adding sales voucher", error: error.message });
  }
};

exports.getAllSales = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);

    const { page = 1, limit = 25 } = req.query;
    const filter = { type: "sales" };

    const totalCount = await Voucher.countDocuments(filter);
    const sales = await Voucher.find(filter)
      .sort({ created_at: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit))
      .lean();

    res.json({
      items: sales,
      totalCount,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(totalCount / limit),
    });
  } catch (error) {
    res.status(500).json({ message: "Error fetching sales vouchers", error: error.message });
  }
};

exports.getSalesById = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);

    const voucher = await Voucher.findOne({ _id: req.params.id, type: "sales" });
    if (!voucher) return res.status(404).json({ message: "Sales voucher not found" });

    const entries = await Entries.find({ "entries.voucherId": voucher._id });

    res.json({ voucher, entries });
  } catch (error) {
    res.status(500).json({ message: "Error fetching sales voucher", error: error.message });
  }
};

exports.updateSalesVoucher = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Stock = getModel(connection, "Stock", stockSchema);

    const { id } = req.params;
    const { date, customerAccount, items, metadata } = req.body;
    const updated_by = req.user?.id;

    const voucher = await Voucher.findOne({ _id: id, type: "sales" });
    if (!voucher) return res.status(404).json({ message: "Sales voucher not found" });

    // Reverse stock for old items
    const oldEntries = await Entries.find({ "entries.voucherId": voucher._id });
    for (const doc of oldEntries) {
      for (const entry of doc.entries) {
        if (entry.type === "sales" && entry.credit && entry.metadata?.itemId) {
          await Stock.findByIdAndUpdate(entry.metadata.itemId, { $inc: { quantity: entry.metadata.quantity } });
        }
      }
    }
    // Remove old entries
    await Entries.deleteMany({ "entries.voucherId": voucher._id });

    // Update voucher fields
    voucher.date = date || voucher.date;
    voucher.accounts = [customerAccount, ...items.map(i => i.account)].filter(Boolean);
    voucher.updated_by = updated_by;
    voucher.metadata = metadata || voucher.metadata;
    await voucher.save();

    // Add new entries and update stock
    const entryDocs = [];
    let totalAmount = 0;
    for (const item of items) {
      entryDocs.push({
        accountId: item.account,
        entries: [{
          voucherId: voucher._id,
          type: "sales",
          description: item.description || "",
          debit: 0,
          credit: item.amount,
          isVoid: false,
          isPosted: true,
          file: "",
          date: date,
          metadata: { ...item.metadata, itemId: item.itemId, quantity: item.quantity },
        }]
      });
      await Stock.findByIdAndUpdate(item.itemId, { $inc: { quantity: -item.quantity } });
      totalAmount += item.amount;
    }
    entryDocs.push({
      accountId: customerAccount,
      entries: [{
        voucherId: voucher._id,
        type: "sales",
        description: "Sales to customer",
        debit: totalAmount,
        credit: 0,
        isVoid: false,
        isPosted: true,
        file: "",
        date: date,
        metadata: {},
      }]
    });

    await Entries.insertMany(entryDocs);

    res.json({ message: "Sales voucher updated", voucher, entries: entryDocs });
  } catch (error) {
    res.status(500).json({ message: "Error updating sales voucher", error: error.message });
  }
};

exports.deleteSalesVoucher = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Voucher = getModel(connection, "Voucher", voucherSchema);
    const Entries = getModel(connection, "Entries", entriesSchema);
    const Stock = getModel(connection, "Stock", stockSchema);

    const { id } = req.params;
    const voucher = await Voucher.findOne({ _id: id, type: "sales" });
    if (!voucher) return res.status(404).json({ message: "Sales voucher not found" });

    // Reverse stock for items
    const entries = await Entries.find({ "entries.voucherId": voucher._id });
    for (const doc of entries) {
      for (const entry of doc.entries) {
        if (entry.type === "sales" && entry.credit && entry.metadata?.itemId) {
          await Stock.findByIdAndUpdate(entry.metadata.itemId, { $inc: { quantity: entry.metadata.quantity } });
        }
      }
    }

    await Voucher.deleteOne({ _id: id });
    await Entries.deleteMany({ "entries.voucherId": voucher._id });

    res.json({ message: "Sales voucher deleted" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting sales voucher", error: error.message });
  }
};
