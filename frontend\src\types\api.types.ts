// API Types for the new backend system

// User and Authentication
export interface User {
  _id: string;
  username: string;
  email: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    username: string;
    email: string;
    role: string;
  };
}

// Category
export interface Category {
  _id: string;
  name: string;
  description?: string;
  createdBy?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
}

// Inventory
export interface InventoryItem {
  _id: string;
  name: string;
  sku: string;
  description?: string;
  quantity: number;
  unit?: string;
  price: number;
  is_deleted: boolean;
  created_by?: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateInventoryRequest {
  name: string;
  sku: string;
  description?: string;
  quantity: number;
  unit?: string;
  price: number;
}

// Voucher
export interface Voucher {
  _id: string;
  voucher_id: string;
  date: string;
  type: 'purchase' | 'sales' | 'purchaseReturn' | 'salesReturn';
  created_by: string;
  updated_by: string;
  accounts: string[];
  is_void: boolean;
  is_posted: boolean;
  is_deleted: boolean;
  branch?: string;
  assignedTo?: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Purchase Voucher
export interface PurchaseVoucherItem {
  itemId: string;
  account: string;
  description: string;
  quantity: number;
  amount: number;
  metadata?: Record<string, any>;
}

export interface CreatePurchaseRequest {
  date: string;
  supplierAccount: string;
  items: PurchaseVoucherItem[];
  metadata?: Record<string, any>;
}

// Sales Voucher
export interface SalesVoucherItem {
  itemId: string;
  account: string;
  description: string;
  quantity: number;
  amount: number;
  metadata?: Record<string, any>;
}

export interface CreateSalesRequest {
  date: string;
  customerAccount: string;
  items: SalesVoucherItem[];
  metadata?: Record<string, any>;
}

// Return Voucher
export interface ReturnVoucherItem {
  itemId: string;
  account: string;
  description: string;
  quantity: number;
  amount: number;
  metadata?: Record<string, any>;
}

export interface CreateReturnRequest {
  date: string;
  type: 'purchaseReturn' | 'salesReturn';
  account: string; // supplier or customer account
  items: ReturnVoucherItem[];
  metadata?: Record<string, any>;
}

// Accounting Entry
export interface AccountingEntry {
  _id: string;
  accountId: string;
  entries: {
    voucherId: string;
    type: string;
    description: string;
    debit: number;
    credit: number;
    isVoid: boolean;
    isPosted: boolean;
    file: string;
    date: string;
    metadata: Record<string, any>;
  }[];
}

// API Response Types
export interface ApiResponse<T = any> {
  success?: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  totalCount: number;
  page: number;
  limit: number;
  totalPages: number;
}

// API Error
export interface ApiError {
  message: string;
  error?: string;
  status?: number;
}

// Stock Summary Types
export interface StockSummary {
  totalItems: number;
  totalValue: number;
  lowStockItems: number;
  outOfStockItems: number;
  categories: {
    name: string;
    count: number;
    value: number;
  }[];
}

// Nil Stock Types
export interface NilStockItem {
  _id: string;
  name: string;
  sku: string;
  category?: string;
  lastUpdated: string;
}

// Void Operations
export interface VoidRequest {
  reason?: string;
}

export interface VoidResponse {
  message: string;
  voucher: Voucher;
}
