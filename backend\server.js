// server.js
const express = require("express");
const app = express();
const cors = require("cors");
require("dotenv").config();

const connectDB = require("./config/db");
const { userSchema } = require("./models/user.model");
const getModel = require("./utils/getModel");
const getDbConnection = require("./utils/dbConnector");

app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:5173"],
  credentials: true,
}));
app.use(express.json());

// Register routes
app.use("/api/auth", require("./routes/auth.routes"));
app.use("/api/categories", require("./routes/category.routes"));
app.use("/api/inventory", require("./routes/inventory.routes"));
app.use("/api/purchases", require("./routes/purchase.routes"));
app.use("/api/sales", require("./routes/sales.routes"));
app.use("/api/returns", require("./routes/return.routes"));
app.use("/api/vouchers", require("./routes/voucher.routes"));

app.get("/", (req, res) => {
  res.json({ success: true, message: "E-Voucher backend running" });
});

const PORT = process.env.PORT || 5000;

// Connect to DB and then start server
connectDB().then(async () => {
  const connection = await getDbConnection(process.env.DB_PREFIX || "default");
  const User = getModel(connection, "User", userSchema);

  app.listen(PORT, () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
  });
}).catch((err) => {
  console.error("❌ Failed to connect to DB:", err.message);
  process.exit(1);
});
