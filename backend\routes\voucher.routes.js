const express = require("express");
const router = express.Router();
const {
  addVoucher,
  getAllVouchers,
  getVoucherById,
  updateVoucher,
  toggleVoucherVoid,
  deleteVoucher,
  entriesTotal,
  getAccountWiseTotals,
  getOpeningBalanceByAccount,
} = require("../controllers/voucherController");
const verifyToken = require("../middleware/auth");

// Define routes
router.post("/add", verifyToken, addVoucher);
router.get("/", verifyToken, getAllVouchers);
router.get("/:id", verifyToken, getVoucherById);
router.put("/:id", verifyToken, updateVoucher);
router.patch("/:voucherId/void", verifyToken, toggleVoucherVoid);
router.delete("/:id", verifyToken, deleteVoucher);
router.get("/entries/total/:id", verifyToken, entriesTotal);
router.post("/account-wise-totals", verifyToken, getAccountWiseTotals);
router.get("/opening-balance/:accountId", verifyToken, getOpeningBalanceByAccount);

module.exports = router;
router.get(
  "/getOpeningVoucher/:accountId",
  // verifyToken,
  getOpeningBalanceByAccount
);

module.exports = router;
router.delete("/:id", verifyToken, deleteVoucher);
router.get("/total/:id", verifyToken, entriesTotal);
router.post("/get-account-wise-totals", verifyToken, getAccountWiseTotals);
router.get(
  "/getOpeningVoucher/:accountId",
  // verifyToken,
  getOpeningBalanceByAccount
);

module.exports = router;
