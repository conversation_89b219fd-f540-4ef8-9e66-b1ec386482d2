const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const { userSchema } = require("../models/user.model");
const getDbConnection = require("../utils/dbConnector");
const getModel = require("../utils/getModel");

exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;
    const connection = await getDbConnection(req.headers.dbprefix);
    const User = getModel(connection, "User", userSchema);

    const user = await User.findOne({ username });
    if (!user || !user.isActive) {
      return res.status(401).json({ message: "Invalid credentials" });
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: "Invalid credentials" });
    }

    const payload = {
      id: user._id,
      username: user.username,
      role: user.role,
    };

    const token = jwt.sign(payload, process.env.JWT_SECRET || "your_jwt_secret", {
      expiresIn: "1d",
    });

    res.json({
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error) {
    res.status(500).json({ message: "Login error", error: error.message });
  }
};
