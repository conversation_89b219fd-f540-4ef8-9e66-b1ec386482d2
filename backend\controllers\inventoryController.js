const getDbConnection = require("../utils/dbConnector");
const { inventorySchema } = require("../models/inventory.model");
const getModel = require("../utils/getModel");

exports.createItem = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Inventory = getModel(connection, "Inventory", inventorySchema);
    const item = new Inventory({ ...req.body, created_by: req.user?.id });
    await item.save();
    res.status(201).json(item);
  } catch (error) {
    res.status(500).json({ message: "Error creating item", error: error.message });
  }
};

exports.getItems = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Inventory = getModel(connection, "Inventory", inventorySchema);
    const items = await Inventory.find({ is_deleted: false });
    res.json(items);
  } catch (error) {
    res.status(500).json({ message: "Error fetching items", error: error.message });
  }
};

exports.getItemById = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Inventory = getModel(connection, "Inventory", inventorySchema);
    const item = await Inventory.findById(req.params.id);
    if (!item || item.is_deleted) return res.status(404).json({ message: "Item not found" });
    res.json(item);
  } catch (error) {
    res.status(500).json({ message: "Error fetching item", error: error.message });
  }
};

exports.updateItem = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Inventory = getModel(connection, "Inventory", inventorySchema);
    const item = await Inventory.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updated_by: req.user?.id, updated_at: new Date() },
      { new: true }
    );
    if (!item) return res.status(404).json({ message: "Item not found" });
    res.json(item);
  } catch (error) {
    res.status(500).json({ message: "Error updating item", error: error.message });
  }
};

exports.deleteItem = async (req, res) => {
  try {
    const connection = await getDbConnection(req.headers.dbprefix);
    const Inventory = getModel(connection, "Inventory", inventorySchema);
    const item = await Inventory.findByIdAndUpdate(
      req.params.id,
      { is_deleted: true, updated_by: req.user?.id, updated_at: new Date() },
      { new: true }
    );
    if (!item) return res.status(404).json({ message: "Item not found" });
    res.json({ message: "Item deleted" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting item", error: error.message });
  }
};
