const mongoose = require("mongoose");

module.exports = function getModel(connection, modelName, schema) {
  if (!modelName || typeof modelName !== "string") {
    throw new Error(
      `getModel: modelName is required and must be a string. Received: ${modelName}`
    );
  }
  try {
    return connection.models[modelName] || 
      connection.model(modelName, schema || require(`../models/${modelName.toLowerCase()}.model`)[`${modelName.charAt(0).toLowerCase() + modelName.slice(1)}Schema`]);
  } catch (err) {
    return require(`../models/${modelName.toLowerCase()}.model`);
  }
};
