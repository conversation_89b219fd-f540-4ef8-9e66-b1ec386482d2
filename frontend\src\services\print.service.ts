// Print Service for Voucher System
// Provides comprehensive printing functionality for all voucher types

export interface PrintableVoucher {
  id: string;
  _id?: string;
  voucher_id?: string;
  date?: string;
  dated?: string;
  description?: string;
  entries?: any[];
  numberOfEntries?: number;
  supplier?: string;
  customer?: string;
  total?: number;
  status?: string;
  type?: string;
  items?: any[];
  metadata?: any;
}

export interface PrintOptions {
  title?: string;
  showHeader?: boolean;
  showFooter?: boolean;
  includeDetails?: boolean;
  format?: 'A4' | 'Letter' | 'Receipt';
}

class PrintService {
  private static instance: PrintService;

  public static getInstance(): PrintService {
    if (!PrintService.instance) {
      PrintService.instance = new PrintService();
    }
    return PrintService.instance;
  }

  /**
   * Print a voucher with formatted layout
   */
  public async printVoucher(voucher: PrintableVoucher, options: PrintOptions = {}): Promise<void> {
    const {
      title = 'Voucher',
      showHeader = true,
      showFooter = true,
      includeDetails = true,
      format = 'A4'
    } = options;

    try {
      // Create a new window for printing
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      if (!printWindow) {
        throw new Error('Unable to open print window. Please check popup blocker settings.');
      }

      // Generate the print content
      const printContent = this.generatePrintContent(voucher, {
        title,
        showHeader,
        showFooter,
        includeDetails,
        format
      });

      // Write content to the print window
      printWindow.document.write(printContent);
      printWindow.document.close();

      // Wait for content to load, then print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 250);
      };

    } catch (error) {
      console.error('Print error:', error);
      alert('Failed to print voucher. Please try again.');
    }
  }

  /**
   * Print voucher list/summary
   */
  public async printVoucherList(vouchers: PrintableVoucher[], listTitle: string = 'Voucher List'): Promise<void> {
    try {
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      if (!printWindow) {
        throw new Error('Unable to open print window. Please check popup blocker settings.');
      }

      const printContent = this.generateListPrintContent(vouchers, listTitle);
      printWindow.document.write(printContent);
      printWindow.document.close();

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 250);
      };

    } catch (error) {
      console.error('Print list error:', error);
      alert('Failed to print voucher list. Please try again.');
    }
  }

  /**
   * Generate HTML content for individual voucher printing
   */
  private generatePrintContent(voucher: PrintableVoucher, options: PrintOptions): string {
    const voucherId = voucher.voucher_id || voucher.id || voucher._id || 'N/A';
    const voucherDate = voucher.date || voucher.dated || new Date().toISOString();
    const formattedDate = new Date(voucherDate).toLocaleDateString('en-GB');
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Print ${options.title} - ${voucherId}</title>
        <style>
          ${this.getPrintStyles(options.format)}
        </style>
      </head>
      <body>
        ${options.showHeader ? this.generateHeader(voucher, options.title) : ''}
        
        <div class="voucher-content">
          <div class="voucher-header">
            <h2>${options.title} Details</h2>
            <div class="voucher-info">
              <div class="info-row">
                <span class="label">Voucher ID:</span>
                <span class="value">${voucherId}</span>
              </div>
              <div class="info-row">
                <span class="label">Date:</span>
                <span class="value">${formattedDate}</span>
              </div>
              ${voucher.description ? `
                <div class="info-row">
                  <span class="label">Description:</span>
                  <span class="value">${voucher.description}</span>
                </div>
              ` : ''}
              ${voucher.supplier ? `
                <div class="info-row">
                  <span class="label">Supplier:</span>
                  <span class="value">${voucher.supplier}</span>
                </div>
              ` : ''}
              ${voucher.customer ? `
                <div class="info-row">
                  <span class="label">Customer:</span>
                  <span class="value">${voucher.customer}</span>
                </div>
              ` : ''}
              ${voucher.status ? `
                <div class="info-row">
                  <span class="label">Status:</span>
                  <span class="value">${voucher.status}</span>
                </div>
              ` : ''}
            </div>
          </div>

          ${options.includeDetails ? this.generateVoucherDetails(voucher) : ''}
        </div>

        ${options.showFooter ? this.generateFooter() : ''}
      </body>
      </html>
    `;
  }

  /**
   * Generate HTML content for voucher list printing
   */
  private generateListPrintContent(vouchers: PrintableVoucher[], listTitle: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Print ${listTitle}</title>
        <style>
          ${this.getPrintStyles('A4')}
          .voucher-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          .voucher-table th, .voucher-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .voucher-table th { background-color: #f5f5f5; font-weight: bold; }
          .voucher-table tr:nth-child(even) { background-color: #f9f9f9; }
        </style>
      </head>
      <body>
        ${this.generateHeader(null, listTitle)}
        
        <div class="voucher-content">
          <h2>${listTitle}</h2>
          <p>Total Records: ${vouchers.length}</p>
          <p>Generated on: ${new Date().toLocaleDateString('en-GB')} at ${new Date().toLocaleTimeString()}</p>
          
          <table class="voucher-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Date</th>
                <th>Description</th>
                <th>Entries</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              ${vouchers.map(voucher => `
                <tr>
                  <td>${voucher.voucher_id || voucher.id || voucher._id || 'N/A'}</td>
                  <td>${new Date(voucher.date || voucher.dated || '').toLocaleDateString('en-GB')}</td>
                  <td>${voucher.description || 'N/A'}</td>
                  <td>${voucher.numberOfEntries || (voucher.entries ? voucher.entries.length : 0)}</td>
                  <td>${voucher.status || 'N/A'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        ${this.generateFooter()}
      </body>
      </html>
    `;
  }

  /**
   * Generate header section
   */
  private generateHeader(voucher: PrintableVoucher | null, title: string): string {
    return `
      <div class="print-header">
        <div class="company-info">
          <h1>EV Voucher System</h1>
          <p>Voucher Management System</p>
        </div>
        <div class="print-info">
          <p>Printed on: ${new Date().toLocaleDateString('en-GB')} at ${new Date().toLocaleTimeString()}</p>
        </div>
      </div>
    `;
  }

  /**
   * Generate voucher details section
   */
  private generateVoucherDetails(voucher: PrintableVoucher): string {
    if (!voucher.entries && !voucher.items) {
      return '<div class="no-details">No detailed entries available</div>';
    }

    const entries = voucher.entries || voucher.items || [];
    
    return `
      <div class="voucher-details">
        <h3>Voucher Entries</h3>
        <table class="details-table">
          <thead>
            <tr>
              <th>Item</th>
              <th>Quantity</th>
              <th>Rate</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            ${entries.map((entry: any) => `
              <tr>
                <td>${entry.item || entry.description || 'N/A'}</td>
                <td>${entry.quantity || entry.qty || 'N/A'}</td>
                <td>${entry.rate || entry.price || 'N/A'}</td>
                <td>${entry.total || entry.amount || 'N/A'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        ${voucher.total ? `<div class="total-amount">Total Amount: ${voucher.total}</div>` : ''}
      </div>
    `;
  }

  /**
   * Generate footer section
   */
  private generateFooter(): string {
    return `
      <div class="print-footer">
        <p>Generated by EV Voucher System - ${new Date().getFullYear()}</p>
      </div>
    `;
  }

  /**
   * Get CSS styles for printing
   */
  private getPrintStyles(format: string = 'A4'): string {
    return `
      @media print {
        @page { 
          margin: 1cm; 
          size: ${format === 'A4' ? 'A4' : format === 'Letter' ? 'letter' : 'A4'}; 
        }
        body { -webkit-print-color-adjust: exact; }
      }
      
      body {
        font-family: Arial, sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
        margin: 0;
        padding: 20px;
      }
      
      .print-header {
        border-bottom: 2px solid #D9E1FA;
        padding-bottom: 15px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .company-info h1 {
        margin: 0;
        color: #333;
        font-size: 24px;
      }
      
      .company-info p {
        margin: 5px 0 0 0;
        color: #666;
      }
      
      .print-info {
        text-align: right;
        font-size: 10px;
        color: #666;
      }
      
      .voucher-content {
        margin-bottom: 30px;
      }
      
      .voucher-header h2 {
        color: #333;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
        margin-bottom: 15px;
      }
      
      .voucher-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-bottom: 20px;
      }
      
      .info-row {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px dotted #ccc;
      }
      
      .label {
        font-weight: bold;
        color: #555;
      }
      
      .value {
        color: #333;
      }
      
      .details-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
      }
      
      .details-table th,
      .details-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
      
      .details-table th {
        background-color: #f5f5f5;
        font-weight: bold;
      }
      
      .details-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      .total-amount {
        text-align: right;
        font-weight: bold;
        font-size: 14px;
        margin-top: 10px;
        padding: 10px;
        background-color: #f0f0f0;
        border: 1px solid #ddd;
      }
      
      .no-details {
        text-align: center;
        color: #666;
        font-style: italic;
        padding: 20px;
      }
      
      .print-footer {
        border-top: 1px solid #ddd;
        padding-top: 15px;
        text-align: center;
        font-size: 10px;
        color: #666;
        margin-top: 30px;
      }
    `;
  }
}

export const printService = PrintService.getInstance();
