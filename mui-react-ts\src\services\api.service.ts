// API Service for the new backend system
import {
  LoginRequest,
  LoginResponse,
  Category,
  CreateCategoryRequest,
  InventoryItem,
  CreateInventoryRequest,
  Voucher,
  CreatePurchaseRequest,
  CreateSalesRequest,
  CreateReturnRequest,
  PaginatedResponse,
  ApiResponse,
  ApiError
} from '../types/api.types';

const API_BASE_URL = 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'dbprefix': 'default' // Multi-tenant support
  };
};

// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Network error' }));
    throw new Error(errorData.message || `HTTP ${response.status}`);
  }
  return response.json();
};

// Authentication API
export const authAPI = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials),
    });
    return handleResponse<LoginResponse>(response);
  },

  // Verify token (if needed)
  verifyToken: async (): Promise<{ valid: boolean }> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/verify`, {
        headers: getAuthHeaders(),
      });
      return { valid: response.ok };
    } catch {
      return { valid: false };
    }
  }
};

// Category API
export const categoryAPI = {
  getAll: async (): Promise<ApiResponse<{ categories: Category[] }>> => {
    const response = await fetch(`${API_BASE_URL}/categories`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  create: async (data: CreateCategoryRequest): Promise<ApiResponse<{ category: Category }>> => {
    const response = await fetch(`${API_BASE_URL}/categories`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  getById: async (id: string): Promise<ApiResponse<Category>> => {
    const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  update: async (id: string, data: Partial<CreateCategoryRequest>): Promise<ApiResponse<Category>> => {
    const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  delete: async (id: string): Promise<ApiResponse> => {
    const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Inventory API
export const inventoryAPI = {
  getAll: async (): Promise<InventoryItem[]> => {
    const response = await fetch(`${API_BASE_URL}/inventory`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  create: async (data: CreateInventoryRequest): Promise<InventoryItem> => {
    const response = await fetch(`${API_BASE_URL}/inventory`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  getById: async (id: string): Promise<InventoryItem> => {
    const response = await fetch(`${API_BASE_URL}/inventory/${id}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  update: async (id: string, data: Partial<CreateInventoryRequest>): Promise<InventoryItem> => {
    const response = await fetch(`${API_BASE_URL}/inventory/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await fetch(`${API_BASE_URL}/inventory/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Purchase Voucher API
export const purchaseAPI = {
  getAll: async (page = 1, limit = 25): Promise<PaginatedResponse<Voucher>> => {
    const response = await fetch(`${API_BASE_URL}/purchases?page=${page}&limit=${limit}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  create: async (data: CreatePurchaseRequest): Promise<{ message: string; voucher: Voucher }> => {
    const response = await fetch(`${API_BASE_URL}/purchases`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  getById: async (id: string): Promise<{ voucher: Voucher; entries: any[] }> => {
    const response = await fetch(`${API_BASE_URL}/purchases/${id}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  update: async (id: string, data: CreatePurchaseRequest): Promise<{ message: string; voucher: Voucher }> => {
    const response = await fetch(`${API_BASE_URL}/purchases/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await fetch(`${API_BASE_URL}/purchases/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Sales Voucher API
export const salesAPI = {
  getAll: async (page = 1, limit = 25): Promise<PaginatedResponse<Voucher>> => {
    const response = await fetch(`${API_BASE_URL}/sales?page=${page}&limit=${limit}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  create: async (data: CreateSalesRequest): Promise<{ message: string; voucher: Voucher }> => {
    const response = await fetch(`${API_BASE_URL}/sales`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  getById: async (id: string): Promise<{ voucher: Voucher; entries: any[] }> => {
    const response = await fetch(`${API_BASE_URL}/sales/${id}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  update: async (id: string, data: CreateSalesRequest): Promise<{ message: string; voucher: Voucher }> => {
    const response = await fetch(`${API_BASE_URL}/sales/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await fetch(`${API_BASE_URL}/sales/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Return Voucher API
export const returnAPI = {
  getAll: async (page = 1, limit = 25): Promise<PaginatedResponse<Voucher>> => {
    const response = await fetch(`${API_BASE_URL}/returns?page=${page}&limit=${limit}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  create: async (data: CreateReturnRequest): Promise<{ message: string; voucher: Voucher }> => {
    const response = await fetch(`${API_BASE_URL}/returns`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  getById: async (id: string): Promise<{ voucher: Voucher; entries: any[] }> => {
    const response = await fetch(`${API_BASE_URL}/returns/${id}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  update: async (id: string, data: CreateReturnRequest): Promise<{ message: string; voucher: Voucher }> => {
    const response = await fetch(`${API_BASE_URL}/returns/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await fetch(`${API_BASE_URL}/returns/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};

// Voucher API (general voucher operations)
export const voucherAPI = {
  getAll: async (page = 1, limit = 25): Promise<PaginatedResponse<Voucher>> => {
    const response = await fetch(`${API_BASE_URL}/vouchers?page=${page}&limit=${limit}`, {
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  void: async (voucherId: string): Promise<{ message: string }> => {
    const response = await fetch(`${API_BASE_URL}/vouchers/${voucherId}/void`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  }
};
