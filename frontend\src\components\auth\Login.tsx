import React, { useState } from "react";
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Alert,
  CircularProgress,
} from "@mui/material";
import { authAPI } from "../../services/api.service";
import { LoginRequest } from "../../types/api.types";

interface LoginProps {
  onLoginSuccess: () => void;
}

const Login: React.FC<LoginProps> = ({ onLoginSuccess }) => {
  const [credentials, setCredentials] = useState<LoginRequest>({
    username: "admin",
    password: "admin@123",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleInputChange = (field: keyof LoginRequest) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCredentials(prev => ({
      ...prev,
      [field]: e.target.value,
    }));
    setError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!credentials.username.trim() || !credentials.password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setLoading(true);
    setError("");

    try {
      console.log('🔐 Attempting login with:', { username: credentials.username });
      const response = await authAPI.login(credentials);

      // Store token and user info
      localStorage.setItem("token", response.token);
      localStorage.setItem("user", JSON.stringify(response.user));

      console.log('✅ Login successful:', response.user);
      onLoginSuccess();
    } catch (err: any) {
      console.error('❌ Login failed:', err);
      setError(err.message || 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      minWidth="100vw"
      sx={{
        background: "#1976d2",
        overflow: "hidden", // Prevent scrollbars
        position: "fixed", // Make the background and content fixed
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
      }}
    >
      <Paper
        elevation={6}
        sx={{
          p: 5,
          minWidth: 350,
          borderRadius: 4,
          boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.2)",
          background: "rgba(255,255,255,0.95)",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <Box
          sx={{
            width: 64,
            height: 64,
            mb: 2,
            borderRadius: "50%",
            background: "linear-gradient(135deg, #B8C5F2 0%, #D9E1FA 100%)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            boxShadow: "0 2px 8px 0 rgba(31, 38, 135, 0.1)",
          }}
        >
          <img src="/vite.svg" alt="Logo" style={{ width: 36, height: 36 }} />
        </Box>
        <Typography
          variant="h5"
          mb={1}
          align="center"
          fontWeight={700}
          color="#2d3a4a"
        >
          Admin Login
        </Typography>
        <Typography variant="body2" mb={2} align="center" color="#6b7280">
          Inventory + Accounting System
        </Typography>
        {error && (
          <Alert severity="error" sx={{ mb: 2, width: "100%" }}>
            {error}
          </Alert>
        )}
        <form onSubmit={handleSubmit} style={{ width: "100%" }}>
          <TextField
            label="Username"
            type="text"
            value={credentials.username}
            onChange={handleInputChange('username')}
            fullWidth
            margin="normal"
            required
            autoFocus
            sx={{
              background: "#f8f9ff",
              borderRadius: 2,
            }}
            disabled={loading}
          />
          <TextField
            label="Password"
            type="password"
            value={credentials.password}
            onChange={handleInputChange('password')}
            fullWidth
            margin="normal"
            required
            sx={{
              background: "#f8f9ff",
              borderRadius: 2,
            }}
            disabled={loading}
          />
          <Button
            type="submit"
            variant="contained"
            color="primary"
            fullWidth
            sx={{
              mt: 2,
              py: 1.2,
              fontWeight: 600,
              fontSize: "1rem",
              borderRadius: 2,
              background: "linear-gradient(135deg, #B8C5F2 0%, #D9E1FA 100%)",
              color: "#2d3a4a",
              boxShadow: "0 2px 8px 0 rgba(31, 38, 135, 0.08)",
              "&:hover": {
                background: "linear-gradient(135deg, #D9E1FA 0%, #B8C5F2 100%)",
              },
            }}
            disabled={loading}
          >
            {loading ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1, color: 'inherit' }} />
                Logging in...
              </>
            ) : (
              "Login"
            )}
          </Button>
        </form>

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="textSecondary">
            Default credentials: admin / admin@123
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default Login;
