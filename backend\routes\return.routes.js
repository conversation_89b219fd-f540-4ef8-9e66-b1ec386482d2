const express = require("express");
const router = express.Router();
const returnController = require("../controllers/returnController");
const verifyToken = require("../middleware/auth");

router.post("/", verifyToken, returnController.addReturnVoucher);
router.get("/", verifyToken, returnController.getAllReturns);
router.get("/:id", verifyToken, returnController.getReturnById);
router.put("/:id", verifyToken, returnController.updateReturn);
router.delete("/:id", verifyToken, returnController.deleteReturn);

module.exports = router;
